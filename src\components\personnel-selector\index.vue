<template>
  <div class="personnel-selector">
    <!-- 人员选择区域 -->
    <div class="personnel-header">
      <div class="section-title">
        <h4>{{ title }}</h4>
        <Button
          v-if="selectedPersonnel && mode === 'edit'"
          @click="openPersonnelModal"
          type="primary"
          size="small"
          class="reselect-btn"
        >
          重新选择
        </Button>
      </div>

      <!-- 未选择人员时的选择区域 - 仅编辑模式显示 -->
      <div class="personnel-select-area" @click="handleSelectionClick" v-if="!selectedPersonnel && mode === 'edit'">
        <div class="personnel-select-icon">
          <Icon type="ios-people" />
        </div>
        <div class="personnel-select-text">{{ placeholder }}</div>
      </div>
      
      <!-- 已选择人员信息展示 -->
      <div class="personnel-info" v-if="selectedPersonnel">
        <!-- 统一宽度的卡片容器 -->
        <div class="unified-cards-container">
          <!-- 人员信息卡片 -->
          <div class="personnel-card">
            <!-- 头像区域 -->
            <div class="avatar-section">
              <div class="avatar-container">
                <img
                  :src="selectedPersonnel.frontPhoto ? http + selectedPersonnel.frontPhoto : defaultImg"
                  alt="人员照片"
                  class="personnel-avatar"
                />
                <div class="personnel-name">{{ formatValue(selectedPersonnel.xm) }}</div>
              </div>
            </div>

            <!-- 信息区域 -->
            <div class="info-section">
              <!-- 监室名称 -->
              <div class="info-item highlight-item">
                <span class="info-label">监室名称</span>
                <span class="info-value highlight-value">{{ formatValue(selectedPersonnel.roomName) }}</span>
              </div>

              <!-- 基本信息 -->
              <div class="info-item">
                <span class="info-label">证件号码</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.zjhm) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">出生日期</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.csrq) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">籍贯</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.jgName) }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">民族</span>
                <span class="info-value">{{ formatValue(selectedPersonnel.mzName) }}</span>
              </div>
            </div>
          </div>

          <!-- 案件信息 -->
          <div class="case-info-section" v-if="showCaseInfo">
            <div class="section-title">
              <h4>案件信息</h4>
            </div>

            <!-- 案件信息卡片 -->
            <div class="case-card">
              <!-- 涉嫌罪名 -->
              <div class="case-item highlight-item">
                <span class="case-label">涉嫌罪名</span>
                <span class="case-value highlight-value">{{ formatValue(selectedPersonnel.xszm) }}</span>
              </div>

              <!-- 案件详情 -->
              <div class="case-item">
                <span class="case-label">案件编号</span>
                <span class="case-value">{{ formatValue(selectedPersonnel.ajbh) }}</span>
              </div>

              <div class="case-item">
                <span class="case-label">诉讼环节</span>
                <span class="case-value">{{ formatValue(selectedPersonnel.sshjName) }}</span>
              </div>

              <div class="case-item">
                <span class="case-label">入所时间</span>
                <span class="case-value">{{ formatValue(selectedPersonnel.rssj) }}</span>
              </div>

              <div class="case-item">
                <span class="case-label">关押期限</span>
                <span class="case-value">{{ formatValue(selectedPersonnel.gyqx) }}</span>
              </div>

              <div class="case-item">
                <span class="case-label">办案单位</span>
                <span class="case-value">{{ formatValue(selectedPersonnel.basj) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 扫码提示 - 仅编辑模式显示 -->
    <div class="scan-tip" v-if="showScanTip && enableScan && mode === 'edit'">
      <Icon type="ios-barcode" size="16" />
      <span v-if="scanPrefix">支持扫码枪扫描条形码自动识别人员（格式：{{ scanPrefix }}人员编码）</span>
      <span v-else>支持扫码枪扫描条形码自动识别人员</span>
    </div>

    <!-- 人员选择弹窗 -->
    <Modal 
      v-model="modalVisible" 
      :mask-closable="false" 
      :closable="true" 
      class-name="select-use-modal" 
      width="1460" 
      title="人员列表"
    >
      <div class="select-use">
        <prisonSelect 
          v-if="modalVisible" 
          ref="prisonSelect" 
          :ryzt="personnelType"
          :isMultiple="isMultiple" 
          :selectUseIds="value"
        />
      </div>
      <div slot="footer">
        <Button type="primary" @click="confirmSelect" class="save">确 定</Button>
        <Button @click="modalVisible = false" class="save">关 闭</Button>
      </div>
    </Modal>


  </div>
</template>

<script>
import {normalizeObject}   from '@/libs/util'
import { prisonSelect } from "sd-prison-select"
//  import  prisonSelect  from "@/components/prisonSelect/index.vue"
export default {
  name: 'personnelSelector',
  components: {
    prisonSelect
  },
  props: {
    // v-model 绑定的人员编码
    value: {
      type: String,
      default: ''
    },
    // 组件模式：edit-编辑模式（默认），detail-详情模式
    mode: {
      type: String,
      default: 'edit',
      validator: function (value) {
        return ['edit', 'detail'].indexOf(value) !== -1
      }
    },
    // 组件标题
    title: {
      type: String,
      default: '被监管人员'
    },
    // 占位符文本
    placeholder: {
      type: String,
      default: '点击选择在押人员'
    },
    // 人员类型过滤 ZS-在所 ALL-全部
    personnelType: {
      type: String,
      default: 'ZS'
    },
    // 是否多选
    isMultiple: {
      type: Boolean,
      default: false
    },
    // 是否显示案件信息
    showCaseInfo: {
      type: Boolean,
      default: true
    },
    // 是否启用扫码功能
    enableScan: {
      type: Boolean,
      default: true
    },
    // 是否显示扫码提示
    showScanTip: {
      type: Boolean,
      default: true
    },
    // 扫码标识符，用于区分扫码和手动输入（可选，设为空字符串则不使用标识符）
    scanPrefix: {
      type: String,
      default: 'bar:'
    },
    // 扫码时间阈值，单位毫秒（用于区分扫码枪快速输入和人工慢速输入）
    timeThreshold: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {
      modalVisible: false,
      selectedPersonnel: null,
      scanCode: '', // 扫码缓存
      lastKeyTime: null, // 上一次按键时间
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp,
      isScanning: false
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && newVal !== this.getCurrentPersonnelCode()) {
          this.loadPersonnelInfo(newVal)
        } else if (!newVal) {
          this.selectedPersonnel = null
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 只有编辑模式才启用扫码功能
    if (this.enableScan && this.mode === 'edit') {
      this.initScanListener()
    }
  },
  beforeDestroy() {
    if (this.scanTimeout) {
      clearTimeout(this.scanTimeout)
    }
    this.removeScanListener()
  },
  methods: {
    /**
     * 处理选择区域点击
     */
    handleSelectionClick() {
      // 只有编辑模式才允许点击选择
      if (this.mode === 'edit') {
        this.openPersonnelModal()
      }
    },

    /**
     * 打开人员选择弹窗
     */
    openPersonnelModal() {
      this.modalVisible = true
    },

    /**
     * 确认选择人员
     */
    confirmSelect() {
      if (this.$refs.prisonSelect && this.$refs.prisonSelect.checkedUse && this.$refs.prisonSelect.checkedUse.length > 0) {
        const selectedData = this.$refs.prisonSelect.checkedUse[0]
        this.selectedPersonnel = selectedData
        this.$emit('input', selectedData.jgrybm)
        this.$emit('change', selectedData, selectedData.jgrybm)
        this.modalVisible = false
      } else {
        this.$Notice.warning({
          title: '提示',
          desc: '请选择人员!'
        })
      }
    },

    /**
     * 获取当前人员编码
     */
    getCurrentPersonnelCode() {
      return this.selectedPersonnel ? this.selectedPersonnel.jgrybm : ''
    },

    /**
     * 加载人员信息
     */
    loadPersonnelInfo(jgrybm) {
      if (!jgrybm) return

      const params = {
        jgrybm: jgrybm,
        ryzt: this.personnelType
      }

      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
        params: params
      }).then(resp => {
        if (resp.code === 0 && resp.data) {
          this.selectedPersonnel = normalizeObject(resp.data)
          this.$emit('change', this.selectedPersonnel, jgrybm)
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg || '获取人员信息失败'
          })
        }
      }).catch(err => {
        console.error('获取人员信息失败:', err)
        this.$Notice.error({
          title: '错误提示',
          desc: '获取人员信息失败'
        })
      })
    },

    /**
     * 初始化扫码监听
     */
    initScanListener() {
      // 只有编辑模式才启用扫码功能
      if (this.enableScan && this.mode === 'edit') {
        // 使用组件实例ID作为标识，避免多个组件间的扫码冲突
        this.componentId = 'personnel-selector-' + Math.random().toString(36).substr(2, 9)
        document.addEventListener('keydown', this.handleGlobalKeyDown)
      }
    },

    /**
     * 移除扫码监听
     */
    removeScanListener() {
      document.removeEventListener('keydown', this.handleGlobalKeyDown)
    },

    /**
     * 处理全局键盘事件 - 基于时间间隔的智能扫码识别
     */
    handleGlobalKeyDown(event) {
      if (!this.enableScan || this.mode !== 'edit') return

      const currentTime = new Date().getTime()

      // 如果是第一次按键或者两次按键间隔超过阈值，则重置扫码缓存
      if (this.lastKeyTime !== null && currentTime - this.lastKeyTime > this.timeThreshold) {
        this.scanCode = ''
      }

      // 排除功能键，只处理可见字符
      if (event.key.length === 1) {
        this.scanCode += event.key
      }

      // 如果按键是 Enter，并且扫码缓存不为空，则认为是扫码结束
      if (event.key === 'Enter' && this.scanCode) {
        console.log('识别到扫码枪输入:', this.scanCode)

        // 处理扫码结果
        this.processScanCode(this.scanCode)

        // 清空扫码缓存，为下一次扫码做准备
        this.scanCode = ''

        // 阻止Enter键的默认行为，比如触发表单提交
        event.preventDefault()
      }

      // 更新上一次按键时间
      this.lastKeyTime = currentTime
    },

    /**
     * 处理扫码结果 - 基于时间间隔的智能识别
     */
    processScanCode(scannedCode) {
      if (!scannedCode || scannedCode.trim() === '') return

      const code = scannedCode.trim()
      console.log('处理扫码内容:', code)

      // 检查是否包含扫码标识符（可选，如果不需要标识符可以去掉这个检查）
      if (this.scanPrefix && !code.startsWith(this.scanPrefix)) {
        console.log('扫码内容无标识符，已忽略:', code)
        return
      }

      // 提取人员编码
      let personnelCode = code
      if (this.scanPrefix) {
        personnelCode = code.replace(this.scanPrefix, '').trim()
      }

      if (personnelCode && personnelCode.length > 0) {
        // 显示扫码成功提示
        this.$Message.info('扫码成功，正在获取人员信息...')

        // 更新人员编码并触发信息加载
        this.$emit('input', personnelCode)
        this.loadPersonnelInfo(personnelCode)
      } else {
        this.$Message.warning('扫码内容格式错误，请重新扫码')
      }
    },

    /**
     * 格式化显示值，处理 null 字符串
     */
    formatValue(value) {
      if (!value || value === 'null' || value === null || value === undefined) {
        return '-'
      }
      return value
    }
  }
}
</script>

<style lang="less" scoped>
@import url('./index.less');
</style>
