<template>
  <div class="prison-select-test">
    <h2>人员选择组件全选功能测试</h2>
    
    <div class="test-controls">
      <h3>测试配置</h3>
      <div style="margin-bottom: 20px;">
        <label>人员状态 (ryzt): </label>
        <Select v-model="testConfig.ryzt" style="width: 200px;">
          <Option value="ALL">全部</Option>
          <Option value="ZS">在所</Option>
        </Select>
      </div>
      
      <div style="margin-bottom: 20px;">
        <label>是否多选 (isMultiple): </label>
        <Switch v-model="testConfig.isMultiple" />
      </div>
      
      <div style="margin-bottom: 20px;">
        <label>选择限制 (limit): </label>
        <InputNumber v-model="testConfig.limit" :min="1" :max="999" style="width: 200px;" />
      </div>
      
      <Button type="primary" @click="updateConfig">更新配置</Button>
      <Button @click="getSelectedUsers" style="margin-left: 10px;">获取已选择人员</Button>
    </div>

    <div class="test-result" v-if="selectedUsers.length > 0">
      <h3>已选择人员 ({{ selectedUsers.length }}人)</h3>
      <div class="selected-list">
        <div v-for="(user, index) in selectedUsers" :key="index" class="selected-item">
          <span>{{ user.roomName }} | {{ user.xm }} ({{ user.jgrybm }})</span>
        </div>
      </div>
    </div>

    <div class="component-wrapper">
      <h3>人员选择组件</h3>
      <prisonSelect 
        ref="prisonSelectRef"
        :ryzt="currentConfig.ryzt"
        :isMultiple="currentConfig.isMultiple"
        :limit="currentConfig.limit"
        :selectUseIds="selectUseIds"
      />
    </div>
  </div>
</template>

<script>
import prisonSelect from '@/components/prisonSelect/index.vue'

export default {
  name: 'PrisonSelectTest',
  components: {
    prisonSelect
  },
  data() {
    return {
      testConfig: {
        ryzt: 'ZS',
        isMultiple: true,
        limit: 50
      },
      currentConfig: {
        ryzt: 'ZS',
        isMultiple: true,
        limit: 2
      },
      selectUseIds: '',
      selectedUsers: []
    }
  },
  methods: {
    updateConfig() {
      this.currentConfig = { ...this.testConfig }
      this.$Notice.success({
        title: '配置更新',
        desc: '组件配置已更新'
      })
    },
    
    getSelectedUsers() {
      if (this.$refs.prisonSelectRef && this.$refs.prisonSelectRef.checkedUse) {
        this.selectedUsers = [...this.$refs.prisonSelectRef.checkedUse]
        this.$Notice.info({
          title: '获取成功',
          desc: `当前已选择 ${this.selectedUsers.length} 名人员`
        })
      } else {
        this.selectedUsers = []
        this.$Notice.warning({
          title: '提示',
          desc: '暂无选择人员'
        })
      }
    }
  }
}
</script>

<style scoped>
.prison-select-test {
  padding: 20px;
  height: 900px;
  overflow-y:auto;
}

.test-controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.test-controls h3 {
  margin-top: 0;
  color: #333;
}

.test-controls label {
  display: inline-block;
  width: 150px;
  font-weight: bold;
}

.test-result {
  background: #e6f7ff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #91d5ff;
}

.test-result h3 {
  margin-top: 0;
  color: #1890ff;
}

.selected-list {
  max-height: 200px;
  overflow-y: auto;
}

.selected-item {
  padding: 5px 0;
  border-bottom: 1px solid #d9d9d9;
}

.selected-item:last-child {
  border-bottom: none;
}

.component-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 20px;
}

.component-wrapper h3 {
  margin-top: 0;
  color: #333;
}
</style>
