<!-- 监所人员选择组件 -->
<template>
  <div class="prison-select">
    <div class="prison-select-left">
      <div v-for="(item, index) in jsData" :key="index">
        <div :class="['prison-select-left-child', openCurRoom == index ? 'active' : '']" @click="openRoom(item, index)">
          <div style="width: 70%;display: flex;align-items: center;">
            <div class="prison-select-left-childDiv">
              <img src="@/assets/images/app-icon.png" style="width:36px;height: 36px;" v-if="item.areaName == '全部'" />
              <Icon type="ios-calculator" :size="40" color="#027bff" v-else />
            </div>
            <span class="jsName" :style="{ 'color': item.areaName != '全部' ? '' : '#3398fe', }">{{ item.areaName
            }}</span>
          </div>
          <Icon :type="openCurRoom == index ? 'ios-arrow-down' : 'ios-arrow-forward'" :size="20"
            v-if="item.areaName != '全部'" style="padding-right: 10px;" />
        </div>
        <div class="roomNameWrap" v-if="openCurRoom == index">
          <p :class="['roomName', page.roomCode == ele.roomCode ? 'active' : '']" v-for="(ele, i) in item.children"
            :key="i + 'areaName'" @click="getUserData(item, ele)">{{ ele.roomName }}</p>
        </div>
      </div>
    </div>
    <div class="prison-select-center">
      <div style="display: flex;padding:0 16px 16px 16px;align-items:center">
        <span class="use-form-title">姓 名：</span>
        <Input v-model="page.xm" style="width: 160px;height: 30px;" @input="search"></Input>
        <span class="use-form-title" style="width: 90px;">&nbsp;&nbsp;入所时间：</span> <el-date-picker size="small"
          type="datetimerange" v-model="page.rssj" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择" style="width: 380px"></el-date-picker>
        <div>
          &nbsp;&nbsp;&nbsp;<Button @click="reset" size="small">重置</Button>
          &nbsp;&nbsp;<Button type="primary" @click="search" size="small">搜索</Button>
          &nbsp;&nbsp;<Button v-if="showSelectAllBtn" type="success" @click="selectAllInRoom" size="small">全选</Button>
        </div>

      </div>
      <div class="use-list" v-if="userList && userList.length > 0">
        <div v-for="(item, index) in userList" :key="index" :class="['use-list-box', item.checked ? 'checked' : '']"
          @click="selectCur(item)">
          <div class="user-flex">
            <p class="rybh">人员编号：<span class="rybhZs" :title="item.rybh">{{ item.rybh }}</span></p><span class="userXb">
              <img :src="item.xb == '男' ? men : women" /></span>
          </div>
          <div class="user-flex-sm">
            <img :src="item.frontPhoto ? http + item.frontPhoto : defaultImg"
              style="width: 88px;height:110px;margin-right: 10px;" />
            <div style="width: 100%;">
              <!-- 房间号： -->
              <p class="user-flex-sm"><span><i class="fontType redColor">{{ item.roomName }}</i></span><span
                  class="ZS">{{ item.ryztName }}</span></p>
              <p><span>铺位号：<i class="fontType blueColor">{{ item.cwh }}</i></span><span class=""></span></p>
              <div class="user-flex">
                <div>
                  <p><span class="userName">{{ item.xm }}</span></p>
                  <p><span v-if="item.age || item.zjhm">{{ item.age ? item.age : calculateAgeFromID(item.zjhm)
                  }}岁</span><span v-else>年龄未知</span>-<span>{{ item.mzName }}</span></p>
                </div>
                <div v-if="item.zzczjg">
                  <p class="grayBg">{{ item.zzczjg }}</p>
                </div>
              </div>
              <p>{{ item.zjhm }}</p>
            </div>
            <div></div>
          </div>
          <div class="user-flex zgWrap">
            <div v-if="item.bar"><span class="grayBg">主管</span>&nbsp;{{ item.zgmjName }}</div>
            <div v-if="item.xg"><span class="grayBg">协管</span>&nbsp;{{ item.xg }}</div>
          </div>
          <Icon type="md-checkmark-circle" :size="26" color="#027bff" v-if="item.checked" class="checkedIcon" />
          <Spin size="large" fix v-if="spinShow"></Spin>
        </div>

      </div>
      <div class="use-list" v-else style="">
        <div style="margin:auto">
          <img src="@/assets/images/noData.png" />
          <p class="noUseData">暂无人员数据</p>
        </div>
      </div>
      <div class="pageWrap">
        <Page :total="total" class="pageWrap" size="small" show-elevator show-sizer :page-size-opts='pageSizeOpts'
          show-total :page-size="page.pageSize" @on-prev="getNo" @on-next="getNo" :current="page.pageNo"
          @on-change="getNo" @on-page-size-change="getSize" />
      </div>
    </div>
    <div class="prison-select-right">
      <p class="prison-select-right-title">已选择人员({{ checkedUse.length }})</p>
      <div class="selectedUseWrap" v-if="checkedUse && checkedUse.length > 0">
        <template>
          <p :key="index + 'userList'" class="selectedUse" v-for="(item, index) in checkedUse">
            <span>{{ item.roomName }}</span>&nbsp;|&nbsp;<span>{{ item.xm }}</span>&nbsp;
            <Icon style="cursor: pointer;" type="md-close-circle" :size="20" color="red"
              @click="cancalSelect(item, index)" />
          </p>
        </template>
      </div>
      <p v-else class="noData">暂未选择人员</p>
    </div>

  </div>
</template>

<script>



export default {
  props: {
    ryzt: {
      type: String, // 是否查询所有人员
      default: 'ALL'
    },
    limit: { // 是否多选
      type: Number,
      default: 999999
    },
    isMultiple: { // 是否多选
      type: Boolean,
      default: true
    },
    bjgrylx: {
      type: String, // 是否查询人员类型
      default: ''
    },
    //
    selectUseIds: {
      type: String, // 已选择人员
      default: '' // '7aaf5d9403bc11f08fec0242ac110002,7aaf581a03bc11f08fec0242ac110002'
    }
  },
  watch: {
    userList: {
      handler(n, o) {
        if (n && n.length > 0) {
          // const uniqueArr =this.checkedUse.filter((item, index) => this.checkedUse.findIndex(i => i.jgrybm === item.jgrybm) === index)
          // this.checkedUse=uniqueArr
          if (this.checkedUse && this.checkedUse.length > 0) {
            this.checkedUse.forEach((item, j) => {
              n.forEach((ele, i) => {
                if (item.jgrybm == ele.jgrybm) {
                  this.$set(this.userList[i], 'checked', true)
                }
              })
            })
          }
        }
      },
      deep: true,
      immediate: true
    },
    selectUseIds: {
      handler(n, o) {
        if (n) {
          this.getAllUserData()
        }
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      spinShow: false,
      defaultImg: require('@/assets/images/main.png'),
      http: serverConfig.severHttp,
      women: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAABjklEQVQ4EcVSsUoDQRB9c3eJQbAwn6BFKlNYqAFtLQ0mGLERsRQLjZXaWgqxSilil4gJaa2FoEUKray1TgRFwuVux9nlclxiTCU4sLezM2/evZld4L+NxgnIV3iLoVIDGMvyYOGqlqc3HXcGkkMHRXwMxtxAWCmQMIid6c9YgnrBSmtQ1NYrvohSYV3oaNBug6feu+qIiZaltyTALdhWWeS2oiRR32jRgY0qpzpdfpLiUzlOyHoFaI19fshXeV9jRpkhkGLbA18TIUE2LYr0lVrBytqgGWI0FLiUu+X5XwkkkQHzgvzxICr3pkCfNtGOtNOBr/YMAdGzDPGlT2Zm4DEMe2ISd/1Ef9ckuapqCrnBDA/WtCCfti7we3pwP41loLIMZjhrCJw4mgRSbi+QGUEFvS8R830kHLrhS8xV/HO5gaJcXSkes8p2DO3uF1blfMGM7nSC0pdZ+ggrAyd8BzZZJz4rCdOh66oi3ABB9OgQbY8q1ohQQQDHZp1nPRcZoUo6BP2AmjJIv5//8/0bMieGFMlZxEQAAAAASUVORK5CYII=',
      men: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAEKADAAQAAAABAAAAEAAAAAA0VXHyAAABuElEQVQ4Eb2SPUsDQRCGZ/aSiIFYKwgWWhgQxMZSrexslCSoBLWQgCDEYDqNJirit2CjYCuSC/kB2hksAhZidTYidtoaUEzudpw1boxyYCNecbs78z5zs+8cwH88tJpvo7V8i9u30C2oY3bGHAMJOwTUrGKIcEdCzHgXQ+daI/Tm52qns9Mk6QSQLBQ4JQTGCPAZHHlWWckNar1rB7RrNjoleGJRwUiFhhCRFECm6bMtKCJQk2cp0qFi7h28GN1EFCAQBxpWYgyHywLhkAjatSfuBQB8CmCkXF2/3txKNUZVTa2AvZIbrqTNTW7TAK/nGhlGcGJfKF+BCNnUCc494sLIg8p5agIpA0CQdCzRagTfomTRFZ8jdtrkxThi3OdkcrM8kQE2dVxz30x0Mtl5KWFLoLiVJDv5/pdM9zL0cSU+l3iWCU8qdOxaQAV5fBdsUp8qIoLUBfcNfqjYPZwqg9+5wUT4VcNq/dZBZTm7x7E4/zAFVQRRnBpBirL7Tj1Uv6+ZqGFO7vOM+4WAJJEc5bmv1wM/9x8FaCPfyom4gr3LkTklMlKRbe5kkg0rqvOvz2eRX3V/LngHpeWkFKJkiGAAAAAASUVORK5CYII=',
      openCurRoom: 1,
      jsData: [],
      total: 0,
      page: {
        pageNo: 1,
        pageSize: 9,
        xm: '',
        rssj: []
      },
      userList: [],
      checkedUse: [],
      curRoom: '',
      pageSizeOpts: [9, 18, 27, 45, 72],
      showSelectAllBtn: false, // 控制全选按钮显示
      currentRoomInfo: null // 存储当前选中的监室信息
    }
  },
  mounted() {
    this.getJqData()
    // this.getAllUserData()
  },
  methods: {
    calculateAgeFromID(id) {
      if (id) {
        if (id.length && id.length !== 18) {
          return ''
        }
        // Extract the birth date from the ID (yyyyMMdd format)
        const birthDateString = id.substring(6, 14) // Extracts yyyyMMdd
        const [year, month, day] = [birthDateString.substring(0, 4), birthDateString.substring(4, 6), birthDateString.substring(6, 8)]
        // Create a Date object for the birth date
        const birthDate = new Date(year, month - 1, day) // Month is 0-based in JavaScript
        // Calculate the age
        const today = new Date()
        let age = today.getFullYear() - birthDate.getFullYear()
        const m = today.getMonth() - birthDate.getMonth()
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
          age-- // Adjust if the current date is before the birthday this year
        }
        return age
      } else {
        return null
      }
    },
    reset() {
      this.$set(this.page, 'xm', '')
      this.$set(this.page, 'rssj', [])
      this.getUserData()
    },
    getNo(pageNo) {
      // console.log(this.page,pageNo,'search')
      this.$set(this.page, 'pageNo', pageNo)
      this.getUserData()
    },
    getSize(pageSize) {
      // console.log(this.page,'search')
      this.$set(this.page, 'pageSize', pageSize)
      this.getUserData()
    },
    search() {
      // console.log(this.page,'search')
      this.getUserData()
    },
    openRoom(item, index) {
      if (this.openCurRoom == index) {
        this.openCurRoom = 0
      } else {
        this.openCurRoom = index
      }
      if (item.areaName == '全部') {
        this.page.areaCode = ''
        this.page.roomCode = ''
        this.page.pageNo = 1
        this.page.pageSize = 9
        // 隐藏全选按钮
        this.showSelectAllBtn = false
        this.currentRoomInfo = null
        this.getUserData()
      }
    },
    getUserData(item, ele) {
      this.spinShow = true
      item ? this.page.areaCode = item.areaCode : ''
      ele ? this.page.roomCode = ele.roomCode : ''

      // 控制全选按钮显示：当选择了具体监室且不是"全部"时显示
      if (item && ele && item.areaName !== '全部') {
        this.showSelectAllBtn = true
        this.currentRoomInfo = { item, ele }
      } else {
        this.showSelectAllBtn = false
        this.currentRoomInfo = null
      }

      if (item && ele) {
        this.page.pageNo = 1
        this.page.pageSize = 9
      }
      let params = {
        areaId: this.page.areaCode,
        jsh: this.page.roomCode,
        ryzt: this.ryzt,
        rssj: this.page.rssj,
        xm: this.page.xm,
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        // orgCode:this.$store.state.common.orgCode,
        bjgrylx: this.bjgrylx // '110000113',//
      }
      this.$store.dispatch('authPostRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompoment',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.userList = resp.data.list
          this.total = resp.data.total
          this.spinShow = false
          //   this.checkedUse=[]
          if (this.selectUseIds && this.userList && this.userList.length > 0) {
            let selectUseIdsArr = this.selectUseIds.split(',')
            this.userList.forEach(item => {
              this.$set(item, 'checked', false)
              selectUseIdsArr.forEach(ele => {
                if (item.jgrybm == ele) {
                  this.$set(item, 'checked', true)
                  return
                }
              })
            })
          }
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
          this.spinShow = false
        }
      })
    },
    getAllUserData() {
      let params = {
        jgrybm: this.selectUseIds,
        ryzt: this.ryzt
      }
      this.$store.dispatch('getRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerListByJgrybm',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          // this.checkedUse=resp.data
          this.$set(this, 'checkedUse', resp.data)
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
          // this.spinShow=false
        }
      })
    },
    getPrisonerSelectCompomenOne(jgrybm) {
      let params = {
        jgrybm: jgrybm,
        ryzt: this.ryzt
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompomenOne',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    getJqData() {
      let params = {
        orgCode: this.$store.state.common.orgCode // '110000113',//
      }
      this.$store.dispatch('authGetRequest', {
        url: '/acp-com/base/area/getAreaListByOrgCode',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          this.jsData = resp.data
          let all = {
            areaCode: 'all', areaName: '全部'
          }
          this.jsData.unshift(all)
          // if (this.jsData && this.jsData.length>1 && this.jsData[1].children) {
          //   this.getUserData(this.jsData[1], this.jsData[1].children[0])
          // } else {
          //   this.openCurRoom = 0
          //   this.getUserData()
          // }
          this.openCurRoom = 0
          this.getUserData()
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
        }
      })
    },
    selectCur(item) {
      // console.log(item,'selectCur')
      if (this.limit == this.checkedUse.length) {
        this.$Notice.warning({
          title: '温馨提示',
          desc: '当前最多可选' + this.limit + '人员'
        })
        return
      }
      if (!this.isMultiple) {
        this.checkedUse = []
        this.userList.forEach(ele => {
          this.$set(ele, 'checked', false)
        })
      }
      if (this.userList.length > 0) {
        this.userList.forEach(ele => {
          if (ele.jgrybm == item.jgrybm) {
            this.$set(ele, 'checked', !ele.checked)
            if (ele.checked) {
              this.checkedUse.push(ele)
            } else {
              this.checkedUse.forEach((item, i) => {
                if (item.jgrybm == ele.jgrybm) {
                  this.checkedUse.splice(i, 1)
                }
              })
            }
          }
        })
      }
    },
    cancalSelect(item, index) {
      if (this.userList.length > 0) {
        this.userList.forEach(ele => {
          if (ele.jgrybm == item.jgrybm) {
            this.$set(ele, 'checked', false)
          }
        })
      }
      this.checkedUse.splice(index, 1)
    },
    // 监室全选功能
    selectAllInRoom() {
      if (!this.currentRoomInfo) {
        this.$Notice.warning({
          title: '温馨提示',
          desc: '请先选择监室'
        })
        return
      }

      const { item, ele } = this.currentRoomInfo

      // 检查是否为单选模式
      if (!this.isMultiple) {
        this.$Notice.warning({
          title: '温馨提示',
          desc: '当前为单选模式，无法使用全选功能'
        })
        return
      }

      this.spinShow = true

      let params = {
        areaId: item.areaCode,
        jsh: ele.roomCode,
        ryzt: this.ryzt,
        rssj: this.page.rssj,
        xm: this.page.xm,
        pageNo: 1,
        pageSize: 999, // 设置大的pageSize获取所有数据
        bjgrylx: this.bjgrylx
      }

      this.$store.dispatch('authPostRequest', {
        url: '/acp-com/base/pm/prisoner/getPrisonerSelectCompoment',
        params: params
      }).then(resp => {
        if (resp.code == 0) {
          const allRoomUsers = resp.data.list || []

          // 检查选择人员数量限制
          const newSelectCount = allRoomUsers.filter(user =>
            !this.checkedUse.some(checked => checked.jgrybm === user.jgrybm)
          ).length

          if (this.checkedUse.length + newSelectCount > this.limit) {
            this.$Notice.warning({
              title: '温馨提示',
              desc: `当前最多可选${this.limit}人员，监室共有${allRoomUsers.length}人，已选${this.checkedUse.length}人，无法全选`
            })
            this.spinShow = false
            return
          }

          // 将监室所有人员添加到已选择列表（去重）
          allRoomUsers.forEach(user => {
            const isAlreadySelected = this.checkedUse.some(checked => checked.jgrybm === user.jgrybm)
            if (!isAlreadySelected) {
              this.checkedUse.push(user)
            }
          })

          // 更新当前页面显示的用户选中状态
          if (this.userList && this.userList.length > 0) {
            this.userList.forEach(user => {
              const isSelected = this.checkedUse.some(checked => checked.jgrybm === user.jgrybm)
              this.$set(user, 'checked', isSelected)
            })
          }

          this.$Notice.success({
            title: '成功提示',
            desc: `已选择监室【${ele.roomName}】的所有${allRoomUsers.length}名人员`
          })

          this.spinShow = false
        } else {
          this.$Notice.error({
            title: '错误提示',
            desc: resp.msg
          })
          this.spinShow = false
        }
      }).catch(() => {
        this.$Notice.error({
          title: '错误提示',
          desc: '获取监室人员数据失败'
        })
        this.spinShow = false
      })
    }
  }
}
</script>

<style lang="less">
@import url('./index.less');
</style>
