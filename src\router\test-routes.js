/**
 * 组件测试路由配置
 * 专门用于测试各种组件和功能页面
 */

export default [{
    path: '/test/business-example',
    name: 'TestBusinessExample',
    component: () => import('@/components/case-personnel-selector/usage-example.vue'),
    meta: {
      title: '提讯页面 - 扫码复用办案人员测试',
      icon: 'ios-document',
      hideInMenu: false
    }
  },
  {
    path: '/test/bsp-system-param',
    name: 'TestBspSystemParam',
    component: () => import('@/view/test/bsp-system-param.test.vue'),
    meta: {
      title: 'BSP系统参数工具类测试',
      icon: 'ios-settings',
      hideInMenu: false
    }
  },
  {
    path: '/test/upload-demo',
    name: 'UploadDemo',
    component: () => import('@/view/UploadDemo.vue'),
    meta: {
      title: '文件上传组件测试',
      icon: 'ios-cloud-upload',
    }
  },
  {
    path: '/test/prison-select',
    name: 'TestPrisonSelect',
    component: () => import('@/view/test/prison-select-test.vue'),
    meta: {
      title: '人员选择组件全选功能测试',
      icon: 'ios-people',
      hideInMenu: false
    }
  },
  // 流程图设计
  {
    path: '/test/workflowEdit',
    name: 'workflowEdit',
    component: () => import('@/components/comAntvX6FlowWork/comAntvX6FlowWork/workFlow.vue'),
    meta: {
      title: '流程图设计',
      icon: 'ios-settings',
      hideInMenu: false
    }
  },
  /*
  {
    path: '/test/rs-dynamic-datagrid',
    name: 'TestRsDynamicDatagrid',
    component: () => import('@/components/rs-dynamic-datagrid/test.vue'),
    meta: {
      title: 'rs-DynamicDatagrid 动态数据表格测试',
      icon: 'ios-grid',
      hideInMenu: false
    }
  },
  {
    path: '/test/dynamic-list/:funcMark',
    name: 'TestDynamicList',
    component: () => import('@/components/rs-dynamic-datagrid/index.vue'),
    meta: {
      title: '动态列表页面',
      icon: 'ios-list',
      hideInMenu: true
    }
  }
  */
]
